<template>
  <div class="dynamic-model-container">
    <div class="function-header">
      <h3>动态更新时空基准模型</h3>
    </div>
    
    <div class="function-content">
      <div class="model-config">
        <div class="config-section">
          <h4>更新策略</h4>
          <t-radio-group v-model="updateStrategy">
            <t-radio value="realtime">实时更新</t-radio>
            <t-radio value="daily">每日更新</t-radio>
            <t-radio value="weekly">每周更新</t-radio>
          </t-radio-group>
        </div>
        
        <div class="config-section">
          <h4>融合参数</h4>
          <div class="param-list">
            <div class="param-row">
              <label>权重分配：</label>
              <t-slider v-model="fusionParams.weight" :min="0" :max="100" />
            </div>
            <div class="param-row">
              <label>阈值设置：</label>
              <t-input-number v-model="fusionParams.threshold" :min="0.1" :max="10" :step="0.1" />
            </div>
          </div>
        </div>
        
        <div class="config-actions">
          <t-button theme="primary" @click="startUpdate">启动更新</t-button>
          <t-button theme="default" @click="stopUpdate">停止更新</t-button>
        </div>
      </div>
      
      <div v-if="showResults" class="model-monitor">
        <div class="monitor-item">
          <h4>更新状态</h4>
          <div class="status-display">
            <div class="status-indicator" :class="updateStatus.class"></div>
            <span>{{ updateStatus.text }}</span>
          </div>
        </div>
        
        <div class="monitor-item">
          <h4>性能指标</h4>
          <div class="metrics-grid">
            <div class="metric-item">
              <span class="metric-label">更新频率：</span>
              <span class="metric-value">{{ metrics.frequency }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">处理延迟：</span>
              <span class="metric-value">{{ metrics.latency }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">精度保持：</span>
              <span class="metric-value">{{ metrics.accuracy }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// 更新策略
const updateStrategy = ref('daily')

// 融合参数
const fusionParams = reactive({
  weight: 50,
  threshold: 2.0
})

// 是否显示结果面板
const showResults = ref(false)

// 更新状态
const updateStatus = reactive({
  text: '待启动',
  class: 'status-waiting'
})

// 性能指标
const metrics = reactive({
  frequency: '0次/小时',
  latency: '0ms',
  accuracy: '未评估'
})

// 启动更新
const startUpdate = () => {
  updateStatus.text = '运行中'
  updateStatus.class = 'status-running'
  showResults.value = true

  metrics.frequency = '12次/小时'
  metrics.latency = '150ms'
  metrics.accuracy = '±1.5cm'
  
  MessagePlugin.success('动态更新已启动')
}

// 停止更新
const stopUpdate = () => {
  updateStatus.text = '已停止'
  updateStatus.class = 'status-stopped'
  
  metrics.frequency = '0次/小时'
  metrics.latency = '0ms'
  
  MessagePlugin.info('动态更新已停止')
}
</script>

<style scoped>
.dynamic-model-container {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.function-header {
  height: 48px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.function-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0;
}

.function-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.model-config {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  max-width: 400px;
  width: 100%;
  height: fit-content;
}

.config-section {
  margin-bottom: 20px;
}

.config-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 12px;
}

.param-list {
  display: grid;
  gap: 16px;
}

.param-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.param-row label {
  min-width: 80px;
  font-size: 14px;
  color: var(--td-text-color-primary);
}

.config-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.model-monitor {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  max-width: 400px;
  width: 100%;
  height: fit-content;
}

.monitor-item {
  margin-bottom: 20px;
}

.monitor-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 12px;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-waiting {
  background: var(--td-text-color-placeholder);
}

.status-running {
  background: var(--td-success-color);
  animation: pulse 2s infinite;
}

.status-stopped {
  background: var(--td-error-color);
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.metrics-grid {
  display: grid;
  gap: 8px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.metric-value {
  color: var(--td-text-color-secondary);
  font-weight: 600;
}

@media (max-width: 768px) {
  .function-content {
    grid-template-columns: 1fr;
  }
  
  .config-actions {
    flex-direction: column;
  }
}
</style>
