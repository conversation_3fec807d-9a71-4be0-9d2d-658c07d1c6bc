<template>
  <div class="placeholder-container">
    <div class="function-header">
      <h3>{{ title }}</h3>
      <p>{{ description }}</p>
    </div>
    
    <div class="function-content">
      <div class="placeholder-content">
        <div class="placeholder-icon">{{ icon }}</div>
        <h4>{{ title }}功能</h4>
        <p>此功能正在开发中，敬请期待...</p>
        <div class="development-info">
          <t-tag theme="warning" variant="light">开发中</t-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// 定义props
const props = defineProps({
  title: {
    type: String,
    default: '功能模块'
  },
  description: {
    type: String,
    default: '功能描述'
  },
  icon: {
    type: String,
    default: '🔧'
  }
})

onMounted(() => {
  console.log(`${props.title}组件已加载`)
})
</script>

<style scoped>
.placeholder-container {
  padding: 16px;
  height: 100%;
}

.function-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.function-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.function-header p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.placeholder-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.placeholder-content p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  margin-bottom: 16px;
}

.development-info {
  margin-top: 12px;
}
</style>
