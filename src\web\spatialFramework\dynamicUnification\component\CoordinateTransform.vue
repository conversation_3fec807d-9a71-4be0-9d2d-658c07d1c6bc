<template>
  <div class="coordinate-transform-container">
    <div class="function-header">
      <h3>厘米级坐标框架转换</h3>
    </div>
    
    <div class="function-content">
      <div class="transform-config">
        <div class="config-section">
          <h4>源坐标系</h4>
          <t-select v-model="sourceSystem" placeholder="选择源坐标系">
            <t-option value="cgcs2000_2000" label="CGCS2000(2000.0)" />
            <t-option value="cgcs2000_2010" label="CGCS2000(2010.0)" />
            <t-option value="wgs84" label="WGS84" />
          </t-select>
        </div>
        
        <div class="config-section">
          <h4>目标坐标系</h4>
          <t-select v-model="targetSystem" placeholder="选择目标坐标系">
            <t-option value="cgcs2000_2000" label="CGCS2000(2000.0)" />
            <t-option value="cgcs2000_2010" label="CGCS2000(2010.0)" />
            <t-option value="wgs84" label="WGS84" />
          </t-select>
        </div>
        
        <div class="config-section">
          <h4>坐标输入</h4>
          <div class="coord-inputs">
            <t-input v-model="coordinates.x" placeholder="X坐标" />
            <t-input v-model="coordinates.y" placeholder="Y坐标" />
            <t-input v-model="coordinates.z" placeholder="Z坐标" />
          </div>
        </div>
        
        <div class="config-actions">
          <t-button theme="primary" @click="performTransform">执行转换</t-button>
          <t-button theme="default" @click="clearInputs">清空输入</t-button>
        </div>
      </div>
      
      <div v-if="showResults" class="transform-results">
        <div class="result-section">
          <h4>转换结果</h4>
          <div class="result-coords">
            <div class="coord-item">
              <span class="coord-label">X:</span>
              <span class="coord-value">{{ results.x }}</span>
            </div>
            <div class="coord-item">
              <span class="coord-label">Y:</span>
              <span class="coord-value">{{ results.y }}</span>
            </div>
            <div class="coord-item">
              <span class="coord-label">Z:</span>
              <span class="coord-value">{{ results.z }}</span>
            </div>
          </div>
        </div>
        
        <div class="result-section">
          <h4>精度评估</h4>
          <div class="accuracy-info">
            <div class="accuracy-item">
              <span>转换精度：</span>
              <span class="accuracy-value">{{ accuracy.transform }}</span>
            </div>
            <div class="accuracy-item">
              <span>残差分析：</span>
              <span class="accuracy-value">{{ accuracy.residual }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// 坐标系选择
const sourceSystem = ref('')
const targetSystem = ref('')

// 输入坐标
const coordinates = reactive({
  x: '',
  y: '',
  z: ''
})

// 转换结果
const results = reactive({
  x: '未转换',
  y: '未转换',
  z: '未转换'
})

// 精度信息
const accuracy = reactive({
  transform: '未评估',
  residual: '未计算'
})

// 执行转换
const performTransform = () => {
  if (!sourceSystem.value || !targetSystem.value) {
    MessagePlugin.warning('请选择源坐标系和目标坐标系')
    return
  }
  
  if (!coordinates.x || !coordinates.y || !coordinates.z) {
    MessagePlugin.warning('请输入完整的坐标信息')
    return
  }
  
  if (sourceSystem.value === targetSystem.value) {
    MessagePlugin.warning('源坐标系和目标坐标系不能相同')
    return
  }
  
  // 模拟坐标转换计算
  const x = parseFloat(coordinates.x)
  const y = parseFloat(coordinates.y)
  const z = parseFloat(coordinates.z)
  
  // 简单的模拟转换（实际应用中需要使用专业的坐标转换算法）
  results.x = (x + Math.random() * 0.02 - 0.01).toFixed(6)
  results.y = (y + Math.random() * 0.02 - 0.01).toFixed(6)
  results.z = (z + Math.random() * 0.02 - 0.01).toFixed(6)
  
  accuracy.transform = '±1.2cm'
  accuracy.residual = '0.8cm'
  
  MessagePlugin.success('坐标转换完成')
}

// 清空输入
const clearInputs = () => {
  coordinates.x = ''
  coordinates.y = ''
  coordinates.z = ''
  
  results.x = '未转换'
  results.y = '未转换'
  results.z = '未转换'
  
  accuracy.transform = '未评估'
  accuracy.residual = '未计算'
}
</script>

<style scoped>
.coordinate-transform-container {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.function-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.function-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.function-header p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}

.function-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.transform-config {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
}

.config-section {
  margin-bottom: 20px;
}

.config-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.coord-inputs {
  display: grid;
  gap: 8px;
}

.config-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.transform-results {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
}

.result-section {
  margin-bottom: 20px;
}

.result-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 12px;
}

.result-coords {
  display: grid;
  gap: 8px;
}

.coord-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--td-bg-color-page);
  border-radius: var(--td-radius-default);
}

.coord-label {
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.coord-value {
  color: var(--td-text-color-secondary);
  font-family: monospace;
}

.accuracy-info {
  display: grid;
  gap: 8px;
}

.accuracy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.accuracy-item:last-child {
  border-bottom: none;
}

.accuracy-value {
  color: var(--td-text-color-secondary);
  font-weight: 600;
}

@media (max-width: 768px) {
  .function-content {
    grid-template-columns: 1fr;
  }
  
  .config-actions {
    flex-direction: column;
  }
}
</style>
