<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('空间服务与应用预测系统启动成功')
})
</script>

<style>
/* 引入配色规范 - 暂时注释掉，使用内联样式 */
/* @import url('./#063078用色规范.css'); */

/* 主要配色变量 */
:root {
  --td-brand-color: #063078;
  --td-brand-color-light: #f1f3ff;
  --td-brand-color-active: #001b52;
  --td-text-color-primary: rgba(0, 0, 0, 0.9);
  --td-text-color-secondary: rgba(0, 0, 0, 0.6);
  --td-text-color-placeholder: rgba(0, 0, 0, 0.4);
  --td-bg-color-page: #eeeeee;
  --td-bg-color-container: #ffffff;
  --td-border-level-1-color: #e7e7e7;
  --td-shadow-1: 0 1px 10px rgba(0, 0, 0, 5%), 0 4px 5px rgba(0, 0, 0, 8%), 0 2px 4px -1px rgba(0, 0, 0, 12%);
  --td-shadow-2: 0 3px 14px 2px rgba(0, 0, 0, 5%), 0 8px 10px 1px rgba(0, 0, 0, 6%), 0 5px 5px -3px rgba(0, 0, 0, 10%);
  --td-radius-default: 3px;
  --td-radius-medium: 6px;
  --td-success-color: #00a870;
  --td-success-color-light: #e8f8f2;
  --td-warning-color: #ed7b2f;
  --td-warning-color-light: #fef3e6;
  --td-error-color: #e34d59;
  --td-error-color-light: #fdecee;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: var(--td-font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--td-bg-color-page);
  color: var(--td-text-color-primary);
}

#app {
  height: 100%;
  width: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--td-scroll-track-color);
}

::-webkit-scrollbar-thumb {
  background: var(--td-scrollbar-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--td-scrollbar-hover-color);
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.p-10 {
  padding: 10px;
}

.p-20 {
  padding: 20px;
}

/* 卡片样式 */
.card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  box-shadow: var(--td-shadow-1);
  border: 1px solid var(--td-border-level-1-color);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--td-border-level-1-color);
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.card-body {
  padding: 20px;
}

/* 按钮样式增强 */
.btn-primary {
  background: var(--td-brand-color) !important;
  border-color: var(--td-brand-color) !important;
}

.btn-primary:hover {
  background: var(--td-brand-color-active) !important;
  border-color: var(--td-brand-color-active) !important;
}

/* 表格样式增强 */
.table-container {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  overflow: hidden;
  box-shadow: var(--td-shadow-1);
}

/* 表单样式增强 */
.form-container {
  background: var(--td-bg-color-container);
  padding: 24px;
  border-radius: var(--td-radius-medium);
  box-shadow: var(--td-shadow-1);
}

/* 页面标题样式 */
.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 20px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  margin-bottom: 24px;
}

/* 状态标签样式 */
.status-success {
  color: var(--td-success-color);
  background: var(--td-success-color-light);
  padding: 2px 8px;
  border-radius: var(--td-radius-small);
  font-size: 12px;
}

.status-warning {
  color: var(--td-warning-color);
  background: var(--td-warning-color-light);
  padding: 2px 8px;
  border-radius: var(--td-radius-small);
  font-size: 12px;
}

.status-error {
  color: var(--td-error-color);
  background: var(--td-error-color-light);
  padding: 2px 8px;
  border-radius: var(--td-radius-small);
  font-size: 12px;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .page-title {
    font-size: 20px;
  }
  
  .card-body {
    padding: 16px;
  }
  
  .form-container {
    padding: 16px;
  }
}
</style>
