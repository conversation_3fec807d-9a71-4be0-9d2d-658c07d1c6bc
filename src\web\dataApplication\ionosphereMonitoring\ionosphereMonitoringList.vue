<template>
  <div class="ionosphere-monitoring-container">
    <!-- 三级菜单Tab -->
    <div class="third-level-tabs">
      <div
        v-for="subFunction in subFunctions"
        :key="subFunction.key"
        class="third-level-tab"
        :class="{ active: activeSubFunction === subFunction.key }"
        @click="handleSubFunctionChange(subFunction.key)"
      >
        {{ subFunction.title }}
      </div>
    </div>

    <!-- 三级菜单内容面板 -->
    <div class="third-level-content">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent } from 'vue'

// 当前激活的子功能
const activeSubFunction = ref('tec-extraction')

// 子功能列表
const subFunctions = ref([
  {
    key: 'tec-extraction',
    title: '区域电离层TEC值提取',
    component: defineAsyncComponent(() => import('./component/TecExtraction.vue'))
  },
  {
    key: 'disturbance-analysis',
    title: '电离层行扰分析',
    component: defineAsyncComponent(() => import('./component/DisturbanceAnalysis.vue'))
  },
  {
    key: 'short-forecast',
    title: '电离层短期预报',
    component: defineAsyncComponent(() => import('./component/ShortForecast.vue'))
  },
  {
    key: 'monitoring-prediction',
    title: '行扰监测与预测',
    component: defineAsyncComponent(() => import('./component/MonitoringPrediction.vue'))
  }
])

// 当前组件
const currentComponent = computed(() => {
  const subFunction = subFunctions.value.find(item => item.key === activeSubFunction.value)
  return subFunction ? subFunction.component : null
})

// 子功能切换处理
const handleSubFunctionChange = (value) => {
  activeSubFunction.value = value
  console.log('切换到子功能:', value)
}

onMounted(() => {
  console.log('电离层监测与预测页面已加载')
})
</script>

<style scoped>
.ionosphere-monitoring-container {
  width: 100%;
  height: 100%;
}

/* 三级菜单Tab样式 */
.third-level-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.third-level-tab {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: var(--td-radius-default);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  color: var(--td-text-color-primary);
  white-space: nowrap;
  backdrop-filter: blur(10px);
  box-shadow: var(--td-shadow-1);
}

.third-level-tab:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--td-brand-color);
  color: var(--td-brand-color);
  transform: translateY(-1px);
  box-shadow: var(--td-shadow-2);
}

.third-level-tab.active {
  background: var(--td-brand-color);
  border-color: var(--td-brand-color);
  color: white;
  box-shadow: var(--td-shadow-2);
}

/* 三级菜单内容面板样式 */
.third-level-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: var(--td-radius-medium);
  box-shadow: var(--td-shadow-2);
  backdrop-filter: blur(10px);
  border: 1px solid var(--td-border-level-1-color);
  max-height: 500px;
  overflow-y: auto;
}
</style>
