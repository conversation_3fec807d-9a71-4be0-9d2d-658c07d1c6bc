<template>
  <div class="sea-level-trend-container">
    <div class="page-header">
      <h2 class="page-title">海平面变化趋势分析</h2>
      <p class="page-subtitle">开展海平面变化趋势和周期性特征分析</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>海平面变化趋势分析功能</h3>
        </div>
        <div class="card-body">
          <div class="trend-features">
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <h4>海平面变化趋势分析</h4>
              <p>开展海平面变化趋势和周期性特征分析</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔄</div>
              <h4>周期性特征分析</h4>
              <p>分析海平面变化的周期性特征</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '海平面变化趋势分析',
  description: '开展海平面变化趋势和周期性特征分析'
})

onMounted(() => {
  console.log('海平面变化趋势分析组件已加载')
})
</script>

<style scoped>
.sea-level-trend-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  box-shadow: var(--td-shadow-1);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0;
}

.card-body {
  padding: 20px;
}

.trend-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
