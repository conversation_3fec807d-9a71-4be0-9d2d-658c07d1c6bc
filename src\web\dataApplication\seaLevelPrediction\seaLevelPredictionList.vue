<template>
  <div class="sea-level-prediction-container">
    <div class="page-header">
      <h1 class="page-title">区域海平面变化预测</h1>
      <p class="page-subtitle">在区域海平面基准归算分析方面，基于特定区域卫星高度计观测数据等数据，构建区域海平面变化预测模型</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>海平面变化预测功能</h3>
        </div>
        <div class="card-body">
          <div class="prediction-features">
            <div class="feature-item">
              <div class="feature-icon">🛰️</div>
              <h4>卫星高度计观测数据</h4>
              <p>基于特定区域卫星高度计观测数据等数据</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🌊</div>
              <h4>潮汐变化规律</h4>
              <p>依据潮汐变化规律</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <h4>海平面变化趋势分析</h4>
              <p>开展海平面变化趋势和周期性特征分析</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔮</div>
              <h4>预测模型构建</h4>
              <p>采用统计预测、多模式集合预测等方法，构建区域海平面变化预测模型</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '区域海平面变化预测',
  description: '基于特定区域卫星高度计观测数据等数据，构建区域海平面变化预测模型'
})

onMounted(() => {
  console.log('区域海平面变化预测页面已加载')
})
</script>

<style scoped>
.sea-level-prediction-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.prediction-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
