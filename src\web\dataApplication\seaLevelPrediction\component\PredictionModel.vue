<template>
  <div class="prediction-model-container">
    <div class="page-header">
      <h2 class="page-title">预测模型构建</h2>
      <p class="page-subtitle">采用统计预测、多模式集合预测等方法，构建区域海平面变化预测模型</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>预测模型构建功能</h3>
        </div>
        <div class="card-body">
          <div class="model-features">
            <div class="feature-item">
              <div class="feature-icon">🔮</div>
              <h4>预测模型构建</h4>
              <p>采用统计预测、多模式集合预测等方法，构建区域海平面变化预测模型</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📈</div>
              <h4>统计预测</h4>
              <p>采用统计预测方法进行海平面变化预测</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🔄</div>
              <h4>多模式集合预测</h4>
              <p>采用多模式集合预测方法提高预测精度</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '预测模型构建',
  description: '采用统计预测、多模式集合预测等方法，构建区域海平面变化预测模型'
})

onMounted(() => {
  console.log('预测模型构建组件已加载')
})
</script>

<style scoped>
.prediction-model-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  box-shadow: var(--td-shadow-1);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0;
}

.card-body {
  padding: 20px;
}

.model-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
