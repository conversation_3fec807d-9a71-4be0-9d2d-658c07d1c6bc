<template>
  <div class="troposphere-monitoring-container">
    <div class="page-header">
      <h1 class="page-title">对流层监测与分析</h1>
      <p class="page-subtitle">针对对流层监测，开展特定区域对流层产品计算，实现对流层监测</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>对流层监测功能</h3>
        </div>
        <div class="card-body">
          <div class="troposphere-features">
            <div class="feature-item">
              <div class="feature-icon">☁️</div>
              <h4>对流层产品计算</h4>
              <p>开展特定区域对流层产品计算</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📈</div>
              <h4>对流层监测</h4>
              <p>实现对流层监测</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">💧</div>
              <h4>可降水量建模反演</h4>
              <p>进行可降水量建模反演评估</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🌊</div>
              <h4>水汽时空变化分析</h4>
              <p>进行水汽时空变化模式分析</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '对流层监测与分析',
  description: '针对对流层监测，开展特定区域对流层产品计算'
})

onMounted(() => {
  console.log('对流层监测与分析页面已加载')
})
</script>

<style scoped>
.troposphere-monitoring-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.troposphere-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
