<template>
  <div class="water-vapor-modeling-container">
    <div class="page-header">
      <h2 class="page-title">可降水量建模反演</h2>
      <p class="page-subtitle">进行可降水量建模反演评估</p>
    </div>
    
    <div class="content-area">
      <div class="card">
        <div class="card-header">
          <h3>可降水量建模反演功能</h3>
        </div>
        <div class="card-body">
          <div class="modeling-features">
            <div class="feature-item">
              <div class="feature-icon">💧</div>
              <h4>可降水量建模反演</h4>
              <p>进行可降水量建模反演评估</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon">📊</div>
              <h4>数据分析</h4>
              <p>对可降水量数据进行深度分析</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const pageData = ref({
  title: '可降水量建模反演',
  description: '进行可降水量建模反演评估'
})

onMounted(() => {
  console.log('可降水量建模反演组件已加载')
})
</script>

<style scoped>
.water-vapor-modeling-container {
  padding: 24px;
  background: var(--td-bg-color-page);
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.6;
}

.card {
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  box-shadow: var(--td-shadow-1);
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.card-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0;
}

.card-body {
  padding: 20px;
}

.modeling-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-item {
  padding: 20px;
  background: var(--td-bg-color-container);
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-item:hover {
  box-shadow: var(--td-shadow-2);
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.feature-item p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}
</style>
