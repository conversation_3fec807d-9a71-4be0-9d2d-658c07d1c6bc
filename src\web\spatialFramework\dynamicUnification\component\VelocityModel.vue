<template>
  <div class="velocity-model-container">
    <div class="function-header">
      <h3>公共高精度板块速度场模型</h3>
      <p>构建公共高精度板块速度场模型，解决地心坐标系 CGCS2000线性动态精化难题</p>
    </div>
    
    <div class="function-content">
      <div class="model-tools">
        <div class="tool-section">
          <h4>模型参数</h4>
          <div class="param-grid">
            <div class="param-item">
              <label>板块类型：</label>
              <t-select v-model="modelParams.plateType" placeholder="选择板块类型">
                <t-option value="eurasian" label="欧亚板块" />
                <t-option value="pacific" label="太平洋板块" />
                <t-option value="indian" label="印度板块" />
              </t-select>
            </div>
            <div class="param-item">
              <label>精度等级：</label>
              <t-select v-model="modelParams.accuracy" placeholder="选择精度等级">
                <t-option value="high" label="高精度(±1cm)" />
                <t-option value="medium" label="中精度(±2cm)" />
                <t-option value="low" label="低精度(±5cm)" />
              </t-select>
            </div>
          </div>
        </div>
        
        <div class="tool-section">
          <h4>计算设置</h4>
          <t-checkbox-group v-model="computeOptions">
            <t-checkbox value="velocity">速度场计算</t-checkbox>
            <t-checkbox value="strain">应变场分析</t-checkbox>
            <t-checkbox value="rotation">旋转参数</t-checkbox>
          </t-checkbox-group>
        </div>
        
        <div class="tool-actions">
          <t-button theme="primary" @click="buildModel">构建模型</t-button>
          <t-button theme="default" @click="validateModel">模型验证</t-button>
        </div>
      </div>
      
      <div class="model-results">
        <div class="result-item">
          <h4>模型状态</h4>
          <div class="model-status">
            <span class="status-label">构建状态：</span>
            <span :class="modelStatus.class">{{ modelStatus.text }}</span>
          </div>
        </div>
        
        <div class="result-item">
          <h4>模型精度</h4>
          <div class="accuracy-info">
            <div class="accuracy-item">
              <span>水平精度：</span>
              <span class="accuracy-value">{{ accuracy.horizontal }}</span>
            </div>
            <div class="accuracy-item">
              <span>垂直精度：</span>
              <span class="accuracy-value">{{ accuracy.vertical }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// 模型参数
const modelParams = reactive({
  plateType: '',
  accuracy: ''
})

// 计算选项
const computeOptions = ref(['velocity', 'strain'])

// 模型状态
const modelStatus = reactive({
  text: '未构建',
  class: 'status-waiting'
})

// 精度信息
const accuracy = reactive({
  horizontal: '未计算',
  vertical: '未计算'
})

// 构建模型
const buildModel = () => {
  if (!modelParams.plateType || !modelParams.accuracy) {
    MessagePlugin.warning('请完善模型参数')
    return
  }
  
  modelStatus.text = '构建中...'
  modelStatus.class = 'status-processing'
  
  setTimeout(() => {
    modelStatus.text = '构建完成'
    modelStatus.class = 'status-success'
    
    accuracy.horizontal = '±1.2cm'
    accuracy.vertical = '±2.1cm'
    
    MessagePlugin.success('速度场模型构建完成')
  }, 3000)
}

// 模型验证
const validateModel = () => {
  if (modelStatus.text === '未构建') {
    MessagePlugin.warning('请先构建模型')
    return
  }
  
  MessagePlugin.success('模型验证功能开发中')
}
</script>

<style scoped>
.velocity-model-container {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.function-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.function-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.function-header p {
  font-size: 14px;
  color: var(--td-text-color-secondary);
  line-height: 1.5;
}

.function-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.model-tools {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
}

.tool-section {
  margin-bottom: 20px;
}

.tool-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 12px;
}

.param-grid {
  display: grid;
  gap: 12px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.param-item label {
  min-width: 80px;
  font-size: 14px;
  color: var(--td-text-color-primary);
}

.tool-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.model-results {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
}

.result-item {
  margin-bottom: 20px;
}

.result-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 12px;
}

.model-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.status-waiting {
  color: var(--td-text-color-placeholder);
}

.status-processing {
  color: var(--td-warning-color);
}

.status-success {
  color: var(--td-success-color);
}

.accuracy-info {
  display: grid;
  gap: 8px;
}

.accuracy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.accuracy-item:last-child {
  border-bottom: none;
}

.accuracy-value {
  color: var(--td-text-color-secondary);
  font-weight: 600;
}

@media (max-width: 768px) {
  .function-content {
    grid-template-columns: 1fr;
  }
  
  .tool-actions {
    flex-direction: column;
  }
}
</style>
