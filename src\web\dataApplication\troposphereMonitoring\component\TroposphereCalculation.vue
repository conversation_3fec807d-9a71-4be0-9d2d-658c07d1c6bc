<template>
  <div class="troposphere-calculation-container">
    <div class="function-header">
      <h3>对流层产品计算</h3>
    </div>
    
    <div class="function-content">
      <div class="analysis-tools">
        <div class="tool-section">
          <h4>计算参数</h4>
          <div class="param-grid">
            <div class="param-item">
              <label>区域选择：</label>
              <t-select v-model="selectedRegion" placeholder="选择计算区域">
                <t-option value="north" label="北方区域" />
                <t-option value="south" label="南方区域" />
                <t-option value="east" label="东部区域" />
                <t-option value="west" label="西部区域" />
              </t-select>
            </div>
          </div>
        </div>

        <div class="tool-actions">
          <t-button theme="primary" @click="startCalculation">开始计算</t-button>
          <t-button theme="default" @click="exportResults">导出结果</t-button>
        </div>
      </div>

      <div v-if="showResults" class="analysis-results">
        <div class="result-item">
          <h4>计算状态</h4>
          <div class="status-info">
            <span class="status-label">当前状态：</span>
            <span :class="calculationStatus.class">{{ calculationStatus.text }}</span>
          </div>
        </div>

        <div class="result-item">
          <h4>计算结果</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">对流层延迟：</span>
              <span class="stat-value">{{ results.delay }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">水汽含量：</span>
              <span class="stat-value">{{ results.waterVapor }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'

// 选中的区域
const selectedRegion = ref('')

// 是否显示结果面板
const showResults = ref(false)

// 计算状态
const calculationStatus = reactive({
  text: '待计算',
  class: 'status-waiting'
})

// 计算结果
const results = reactive({
  delay: '未计算',
  waterVapor: '未计算'
})

// 开始计算
const startCalculation = () => {
  if (!selectedRegion.value) {
    MessagePlugin.warning('请选择计算区域')
    return
  }

  calculationStatus.text = '计算中...'
  calculationStatus.class = 'status-processing'
  showResults.value = true

  setTimeout(() => {
    calculationStatus.text = '计算完成'
    calculationStatus.class = 'status-success'

    results.delay = '2.3m'
    results.waterVapor = '15.6mm'

    MessagePlugin.success('对流层产品计算完成')
  }, 2000)
}

// 导出结果
const exportResults = () => {
  if (calculationStatus.text === '待计算') {
    MessagePlugin.warning('请先进行计算')
    return
  }

  MessagePlugin.success('结果导出功能开发中')
}

onMounted(() => {
  console.log('对流层产品计算组件已加载')
})
</script>

<style scoped>
.troposphere-calculation-container {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.function-header {
  height: 48px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.function-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin: 0;
}

.function-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.analysis-tools {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  max-width: 400px;
  width: 100%;
  height: fit-content;
}

.tool-section {
  margin-bottom: 20px;
}

.tool-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.param-grid {
  display: grid;
  gap: 12px;
}

.param-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.param-item label {
  min-width: 80px;
  font-size: 14px;
  color: var(--td-text-color-primary);
}

.tool-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.analysis-results {
  background: var(--td-bg-color-container);
  padding: 20px;
  border-radius: var(--td-radius-medium);
  border: 1px solid var(--td-border-level-1-color);
  max-width: 400px;
  width: 100%;
  height: fit-content;
}

.result-item {
  margin-bottom: 20px;
}

.result-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--td-text-color-primary);
  margin-bottom: 12px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.status-waiting {
  color: var(--td-text-color-placeholder);
}

.status-processing {
  color: var(--td-warning-color);
}

.status-success {
  color: var(--td-success-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.stat-value {
  color: var(--td-text-color-secondary);
  font-weight: 600;
}

@media (max-width: 768px) {
  .function-content {
    flex-direction: column;
  }

  .analysis-tools,
  .analysis-results {
    max-width: 100%;
  }

  .tool-actions {
    flex-direction: column;
  }
}
</style>
