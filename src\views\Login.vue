<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="login-background">
      <div class="bg-decoration"></div>
      <div class="bg-particles"></div>
    </div>
    
    <!-- 登录表单区域 -->
    <div class="login-content">
      <!-- 系统标题 -->
      <div class="login-header">
        <div class="system-logo">
          <div class="logo-icon"></div>
        </div>
        <h1 class="system-title">空间服务与应用预测系统</h1>
        <p class="system-subtitle">Spatial Service and Application Prediction System</p>
      </div>
      
      <!-- 登录表单 -->
      <div class="login-form-wrapper">
        <t-form
          ref="loginForm"
          :data="formData"
          :rules="formRules"
          :label-width="0"
          class="login-form"
          @submit="handleLogin"
        >
          <t-form-item name="username">
            <t-input
              v-model="formData.username"
              placeholder="请输入用户名"
              size="large"
              clearable
              :prefix-icon="UserIcon"
              class="login-input"
            />
          </t-form-item>
          
          <t-form-item name="password">
            <t-input
              v-model="formData.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              clearable
              :prefix-icon="LockOnIcon"
              class="login-input"
            />
          </t-form-item>
          
          <t-form-item name="captcha" v-if="showCaptcha">
            <div class="captcha-wrapper">
              <t-input
                v-model="formData.captcha"
                placeholder="请输入验证码"
                size="large"
                clearable
                prefix-icon="safety"
                class="captcha-input"
              />
              <div class="captcha-image" @click="refreshCaptcha">
                <span>{{ captchaText }}</span>
              </div>
            </div>
          </t-form-item>
          
          <t-form-item>
            <div class="login-options">
              <t-checkbox v-model="rememberMe">记住密码</t-checkbox>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>
          </t-form-item>
          
          <t-form-item>
            <t-button
              type="submit"
              theme="primary"
              size="large"
              :loading="loginLoading"
              block
              class="login-button"
            >
              {{ loginLoading ? '登录中...' : '登录' }}
            </t-button>
          </t-form-item>
        </t-form>
      </div>
      
      <!-- 页脚信息 -->
      <div class="login-footer">
        <p>&copy; 2025 空间服务与应用预测系统. All rights reserved.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { UserIcon, LockOnIcon } from 'tdesign-icons-vue-next'

const router = useRouter()

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  captcha: ''
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', type: 'error' },
    { min: 3, message: '用户名至少3个字符', type: 'error' }
  ],
  password: [
    { required: true, message: '请输入密码', type: 'error' },
    { min: 6, message: '密码至少6个字符', type: 'error' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', type: 'error' }
  ]
}

// 状态管理
const loginLoading = ref(false)
const rememberMe = ref(false)
const showCaptcha = ref(true)
const captchaText = ref('ABCD')
const loginForm = ref(null)

// 刷新验证码
const refreshCaptcha = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  captchaText.value = result
}

// 登录处理
const handleLogin = async ({ validateResult }) => {
  if (validateResult === true) {
    loginLoading.value = true
    
    try {
      // 模拟登录API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // 验证码校验
      if (formData.captcha.toUpperCase() !== captchaText.value) {
        MessagePlugin.error('验证码错误')
        refreshCaptcha()
        loginLoading.value = false
        return
      }
      
      // 简单的用户名密码验证（实际项目中应该调用后端API）
      if (formData.username === 'admin' && formData.password === '123456') {
        // 保存登录状态
        const token = 'mock-jwt-token-' + Date.now()
        localStorage.setItem('token', token)
        localStorage.setItem('username', formData.username)
        
        if (rememberMe.value) {
          localStorage.setItem('rememberMe', 'true')
          localStorage.setItem('savedUsername', formData.username)
        }
        
        MessagePlugin.success('登录成功')
        
        // 跳转到主页面
        setTimeout(() => {
          router.push('/')
        }, 500)
      } else {
        MessagePlugin.error('用户名或密码错误')
        refreshCaptcha()
      }
    } catch (error) {
      MessagePlugin.error('登录失败，请重试')
      refreshCaptcha()
    } finally {
      loginLoading.value = false
    }
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 检查是否记住密码
  if (localStorage.getItem('rememberMe') === 'true') {
    formData.username = localStorage.getItem('savedUsername') || ''
    rememberMe.value = true
  }
  
  // 初始化验证码
  refreshCaptcha()
})
</script>

<style scoped>
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, var(--td-brand-color-9) 0%, var(--td-brand-color-7) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.bg-decoration {
  position: absolute;
  top: -50%;
  right: -20%;
  width: 80%;
  height: 150%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.bg-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.2), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.4), transparent);
  background-repeat: repeat;
  background-size: 100px 100px;
  animation: particles 20s linear infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes particles {
  0% { transform: translate(0, 0); }
  100% { transform: translate(-100px, -100px); }
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.system-logo {
  margin-bottom: 20px;
}

.logo-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto;
  background: var(--td-brand-color-9);
  border-radius: 16px;
  position: relative;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  background: white;
  border-radius: 8px;
}

.system-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--td-brand-color-9);
  margin-bottom: 8px;
}

.system-subtitle {
  font-size: 14px;
  color: var(--td-text-color-secondary);
}

.login-form-wrapper {
  margin-bottom: 30px;
}

.login-input {
  margin-bottom: 20px;
}

.captcha-wrapper {
  display: flex;
  gap: 12px;
}

.captcha-input {
  flex: 1;
}

.captcha-image {
  width: 100px;
  height: 40px;
  background: var(--td-gray-color-2);
  border: 1px solid var(--td-component-border);
  border-radius: var(--td-radius-default);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: bold;
  color: var(--td-brand-color-9);
  user-select: none;
  transition: all 0.2s;
}

.captcha-image:hover {
  background: var(--td-gray-color-3);
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.forgot-password {
  color: var(--td-brand-color);
  text-decoration: none;
  font-size: 14px;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.login-footer {
  text-align: center;
  color: var(--td-text-color-placeholder);
  font-size: 12px;
}
</style>
