import { PropType } from 'vue';
import '../style/css';
declare const _default: import("vue").DefineComponent<{
    size: {
        type: StringConstructor;
    };
    onClick: {
        type: PropType<(context: {
            e: MouseEvent;
        }) => void>;
    };
}, () => import("vue").VNode<import("vue").RendererNode, import("vue").RendererElement, {
    [key: string]: any;
}>, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<{} & {
    size?: string;
    onClick?: (context: {
        e: MouseEvent;
    }) => void;
}>, {}>;
export default _default;
